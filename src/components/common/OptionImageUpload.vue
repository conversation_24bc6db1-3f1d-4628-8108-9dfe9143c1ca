<template>
  <q-dialog v-model="dialogModel" persistent>
    <div class="q-pa-md" style="min-width: 400px">
      <div class="text-h6 q-mb-md">Upload Option Image</div>

      <!-- Drop Zone -->
      <div
        class="q-mt-md q-mb-md drop-zone q-pa-lg flex flex-center column"
        @dragover.prevent
        @drop.prevent="handleDrop"
      >
        <q-icon name="image" size="246px" color="grey-4" />
        <div class="upload-wrapper">
          <q-btn
            class="rounded-borders q-px-xl q-py-sm"
            label="เปิดโฟลเดอร์"
            no-caps
            text-color="white"
            style="
              border-radius: 12px;
              font-weight: 600;
              position: relative;
              min-width: 200px;
              background-color: #3d3c91;
            "
          >
            <q-file
              v-model="imageFiles"
              accept="image/*"
              class="file-overlay"
              @update:model-value="handleFiles"
              data-cy="upload_option_image_btn"
            />
          </q-btn>
          <div class="text-subttile text-grey q-mt-sm">หรือลากไฟล์มาที่นี่</div>
        </div>
      </div>

      <!-- Action buttons -->
      <div class="row justify-end q-gutter-sm">
        <q-btn flat label="Cancel" @click="close" />
      </div>
    </div>
  </q-dialog>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { OptionService } from 'src/services/asm/optionService';
import type { Option } from 'src/types/models';

const props = defineProps<{
  modelValue: boolean;
  optionId: number | null;
  itemBlockId: number;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void;
  (e: 'image-uploaded', option: Option): void;
}>();

const dialogModel = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

const imageFiles = ref<File | null>(null);

// Utility function to extract image dimensions
async function getImageDimensions(file: File): Promise<{ width: number; height: number }> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    const url = URL.createObjectURL(file);

    img.onload = () => {
      URL.revokeObjectURL(url);
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight,
      });
    };

    img.onerror = () => {
      URL.revokeObjectURL(url);
      reject(new Error('Failed to load image for dimension extraction'));
    };

    img.src = url;
  });
}

// Utility function to resize image to 200x200 while maintaining aspect ratio
function resizeImageToConstraints(
  originalWidth: number,
  originalHeight: number,
  maxWidth: number = 200,
  maxHeight: number = 200,
): { width: number; height: number } {
  // Calculate aspect ratio
  const aspectRatio = originalWidth / originalHeight;

  let newWidth = originalWidth;
  let newHeight = originalHeight;

  // If image is larger than constraints, scale it down
  if (originalWidth > maxWidth || originalHeight > maxHeight) {
    if (aspectRatio > 1) {
      // Landscape: width is larger
      newWidth = maxWidth;
      newHeight = Math.round(maxWidth / aspectRatio);
    } else {
      // Portrait: height is larger
      newHeight = maxHeight;
      newWidth = Math.round(maxHeight * aspectRatio);
    }
  }

  return { width: newWidth, height: newHeight };
}

function close() {
  dialogModel.value = false;
}

function handleDrop(event: DragEvent) {
  const files = event.dataTransfer?.files;
  if (files && files.length > 0) {
    const file = files[0];
    if (file) {
      imageFiles.value = file;
      void handleFiles(file);
    }
  }
}

async function handleFiles(file: File | null) {
  if (!file) {
    console.error('❌ Cannot upload option image: missing file');
    return;
  }

  if (!props.optionId) {
    console.error('❌ Cannot upload option image: missing optionId');
    return;
  }

  try {
    console.log('🎯 Starting option image upload process:', {
      optionId: props.optionId,
      itemBlockId: props.itemBlockId,
      fileName: file.name,
      fileSize: file.size,
    });

    // Extract image dimensions
    const originalDimensions = await getImageDimensions(file);
    console.log('📐 Original image dimensions:', originalDimensions);

    // Resize to 200x200 constraints while maintaining aspect ratio
    const constrainedDimensions = resizeImageToConstraints(
      originalDimensions.width,
      originalDimensions.height,
      200,
      200,
    );
    console.log('📐 Constrained image dimensions:', constrainedDimensions);

    // Initialize option service
    const optionService = new OptionService();

    // Update the option with the image file and constrained dimensions
    const updatedOption = await optionService.updateOption(
      props.optionId,
      {
        itemBlockId: props.itemBlockId,
        // Note: We don't need to send imageWidth/imageHeight for options
        // The backend will handle the image processing and storage
      },
      file,
    );

    console.log('✅ Option image uploaded successfully:', {
      optionId: props.optionId,
      imagePath: updatedOption.imagePath,
      updatedOption,
    });

    // Emit success event with the updated option
    emit('image-uploaded', updatedOption);
    close();
  } catch (error) {
    console.error('❌ Failed to upload option image:', error);
  }
}
</script>

<style scoped>
.drop-zone {
  border: 2px dashed #ccc;
  border-radius: 8px;
  background-color: #f9f9f9;
  transition: border-color 0.3s ease;
}

.drop-zone:hover {
  border-color: #3d3c91;
}

.upload-wrapper {
  text-align: center;
}

.file-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
}

.file-overlay :deep(.q-field__control) {
  height: 100%;
}

.file-overlay :deep(.q-field__native) {
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}
</style>
